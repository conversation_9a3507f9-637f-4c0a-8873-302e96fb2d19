import { useState, useEffect, useMemo } from "react"
import { useSearchParams } from "react-router-dom"
import { <PERSON>h, <PERSON>, Users, Menu, WifiOff } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"

import { She<PERSON>, <PERSON>et<PERSON>ontent, SheetHeader, SheetTitle } from "@/components/ui/sheet"
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from "@/components/ui/resizable"
import { useIsMobile } from "@/hooks/use-mobile"
import { useTranslations } from "@/lib/i18n/typed-translations"
import { useWebSocket } from "@/lib/websocket"
import { useChatChannels, useChatChannel } from "@/hooks/chat"
import { ChannelList, MessageList, ChannelMembersModal } from "@/components/chat"
import { DeepLinkUtils } from "@/lib/notification-navigation"

interface ChatTabProps {
  hubId: number
}

export function ChatTab({ hubId }: ChatTabProps) {
  const [searchParams, setSearchParams] = useSearchParams()
  const [selectedChannelId, setSelectedChannelId] = useState<number | null>(null)
  const [isMobileChannelsOpen, setIsMobileChannelsOpen] = useState(false)
  const [showMembersModal, setShowMembersModal] = useState(false)
  const [panelSizes, setPanelSizes] = useState<number[]>([25, 75])
  const isMobile = useIsMobile()
  const { t, keys } = useTranslations()
  const { isConnected, isConnecting } = useWebSocket()

  // Load saved panel sizes from localStorage
  useEffect(() => {
    const savedSizes = localStorage.getItem('chat-panel-sizes')
    if (savedSizes) {
      try {
        const sizes = JSON.parse(savedSizes)
        if (Array.isArray(sizes) && sizes.length === 2) {
          setPanelSizes(sizes)
        }
      } catch (error) {
        console.warn('Failed to parse saved panel sizes:', error)
      }
    }
  }, [])

  // Save panel sizes to localStorage
  const handlePanelResize = (sizes: number[]) => {
    setPanelSizes(sizes)
    localStorage.setItem('chat-panel-sizes', JSON.stringify(sizes))
  }

  // Fetch channels for this hub
  const { data: channelsResponse, isLoading: channelsLoading } = useChatChannels(hubId)
  const channels = useMemo(() => channelsResponse?.content || [], [channelsResponse?.content])

  // Fetch selected channel details
  const { data: selectedChannel } = useChatChannel(
    hubId,
    selectedChannelId!,
    { enabled: !!selectedChannelId }
  )

  // Handle deep linking to specific chat channels
  useEffect(() => {
    const chatId = DeepLinkUtils.getChatToSelect(searchParams)

    if (chatId && channels.length > 0) {
      // Check if the specified chat exists in the channels
      const targetChannel = channels.find(channel => channel.id === chatId)
      if (targetChannel) {
        setSelectedChannelId(chatId)
        // Clear the chat parameter from URL after selecting
        const newParams = DeepLinkUtils.updateSearchParams(searchParams, {
          chat: null,
        })
        setSearchParams(newParams, { replace: true })
        return
      }
    }

    // Auto-select first channel when channels load (if no specific channel requested)
    if (channels.length > 0 && !selectedChannelId && !chatId) {
      setSelectedChannelId(channels[0].id!);
    }
  }, [channels, selectedChannelId, searchParams, setSearchParams])





  const getChannelIcon = (scope: string) => {
    switch (scope) {
      case "admins": return <Lock className="h-4 w-4" />
      case "creator_specific": return <Users className="h-4 w-4" />
      default: return <Hash className="h-4 w-4" />
    }
  }

  const getChannelName = (channel: { name?: string; scope?: string } | null | undefined) => {
    if (channel?.name) return channel.name;

    switch (channel?.scope) {
      case 'admins': return t(keys.collaborationHubs.chat.adminsOnly);
      case 'admins_reviewers': return t(keys.collaborationHubs.chat.contentReview);
      case 'creator_specific': return t(keys.collaborationHubs.chat.workspace);
      default: return t(keys.collaborationHubs.chat.general);
    }
  }

  const handleChannelSelect = (channelId: number) => {
    setSelectedChannelId(channelId);
    if (isMobile) {
      setIsMobileChannelsOpen(false);
    }
  }

  // Connection status indicator
  const ConnectionStatus = () => {
    if (isConnecting) {
      return (
        <div className="flex items-center gap-2 text-xs text-muted-foreground">
          <WifiOff className="h-3 w-3 animate-pulse" />
          <span>{t(keys.collaborationHubs.chat.reconnecting)}</span>
        </div>
      );
    }

    if (!isConnected) {
      return (
        <div className="flex items-center gap-2 text-xs text-destructive">
          <WifiOff className="h-3 w-3" />
          <span>{t(keys.collaborationHubs.chat.disconnected)}</span>
        </div>
      );
    }

    return null;
  }

  if (channelsLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center space-y-2">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-sm text-muted-foreground">{t(keys.collaborationHubs.chat.loading)}</p>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Mobile Channels Sheet */}
      <Sheet open={isMobileChannelsOpen} onOpenChange={setIsMobileChannelsOpen}>
        <SheetContent side="left" className="w-80 p-0">
          <SheetHeader className="p-4 pb-0">
            <SheetTitle>{t(keys.collaborationHubs.chat.channels)}</SheetTitle>
          </SheetHeader>
          <ScrollArea className="flex-1 p-4">
            <ChannelList
              hubId={hubId}
              selectedChannelId={selectedChannelId || undefined}
              onChannelSelect={handleChannelSelect}
            />
          </ScrollArea>
        </SheetContent>
      </Sheet>

      {/* Desktop Layout with Resizable Panels */}
      {!isMobile ? (
        <ResizablePanelGroup
          direction="horizontal"
          className="h-full max-h-[calc(100vh-12rem)]"
          onLayout={handlePanelResize}
        >
          {/* Channels Panel */}
          <ResizablePanel defaultSize={panelSizes[0]} minSize={20} maxSize={40}>
            <div className="flex flex-col h-full pr-3">
              <div className="mb-4">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-semibold">{t(keys.collaborationHubs.chat.channels)}</h3>
                  <ConnectionStatus />
                </div>
                <ChannelList
                  hubId={hubId}
                  selectedChannelId={selectedChannelId || undefined}
                  onChannelSelect={handleChannelSelect}
                />
              </div>
            </div>
          </ResizablePanel>

          {/* Resizable Handle */}
          <ResizableHandle withHandle className="w-2 hover:bg-muted/50 transition-colors" />

          {/* Chat Panel */}
          <ResizablePanel defaultSize={panelSizes[1]} minSize={60}>
            <div className="flex flex-col h-full pl-3">
              {selectedChannelId ? (
                <>
                  {/* Chat Header - Sticky */}
                  <div className="sticky top-0 z-10 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b mb-4">
                    <div className="flex items-center justify-between py-4">
                      <div className="flex items-center gap-3">
                        {getChannelIcon(selectedChannel?.scope || "admins_reviewers")}
                        <div>
                          <h3 className="font-semibold">
                            {getChannelName(selectedChannel)}
                          </h3>
                          <p className="text-sm text-muted-foreground">
                            {selectedChannel?.participant_count || 0} {t(keys.collaborationHubs.chat.participants)}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="hidden sm:flex"
                          onClick={() => setShowMembersModal(true)}
                        >
                          <Users className="h-4 w-4 mr-2" />
                          <span className="hidden md:inline">{t(keys.collaborationHubs.chat.viewMembers)}</span>
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* Messages */}
                  <MessageList
                    channelId={selectedChannelId}
                    hubId={hubId}
                    channelName={getChannelName(selectedChannel)}
                    className="flex-1"
                  />
                </>
              ) : (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center space-y-2">
                    <p className="text-muted-foreground">{t(keys.collaborationHubs.chat.noMessages)}</p>
                    <p className="text-sm text-muted-foreground">
                      {t(keys.collaborationHubs.chat.noMessagesDescription)}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </ResizablePanel>
        </ResizablePanelGroup>
      ) : (
        /* Mobile Layout - Keep existing mobile layout */
        <div className="flex h-full max-h-[calc(100vh-12rem)]">
          {/* Chat Area - Full width on mobile */}
          <div className="flex-1 flex flex-col min-h-0">
            {selectedChannelId ? (
              <>
                {/* Chat Header - Sticky */}
                <div className="sticky top-0 z-10 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b mb-4">
                  <div className="flex items-center justify-between py-4">
                    <div className="flex items-center gap-3">
                      {/* Mobile Channels Toggle Button */}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setIsMobileChannelsOpen(true)}
                        className="h-8 w-8 p-0"
                      >
                        <Menu className="h-4 w-4" />
                      </Button>

                      {getChannelIcon(selectedChannel?.scope || "admins_reviewers")}
                      <div>
                        <h3 className="font-semibold">
                          {getChannelName(selectedChannel)}
                        </h3>
                        <p className="text-sm text-muted-foreground">
                          {selectedChannel?.participant_count || 0} {t(keys.collaborationHubs.chat.participants)}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <ConnectionStatus />
                      {/* Mobile View Members Button */}
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-8 w-8 p-0"
                        onClick={() => setShowMembersModal(true)}
                      >
                        <Users className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Messages */}
                <MessageList
                  channelId={selectedChannelId}
                  hubId={hubId}
                  channelName={getChannelName(selectedChannel)}
                  className="flex-1"
                />
              </>
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center space-y-2">
                  <p className="text-muted-foreground">{t(keys.collaborationHubs.chat.noMessages)}</p>
                  <p className="text-sm text-muted-foreground">
                    {t(keys.collaborationHubs.chat.noMessagesDescription)}
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Channel Members Modal */}
      {selectedChannelId && (
        <ChannelMembersModal
          hubId={hubId}
          channelId={selectedChannelId}
          channelName={getChannelName(selectedChannel)}
          isOpen={showMembersModal}
          onClose={() => setShowMembersModal(false)}
        />
      )}
    </>
  )
}
