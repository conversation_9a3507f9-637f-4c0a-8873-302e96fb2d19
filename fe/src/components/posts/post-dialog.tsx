import { useState, useEffect, useRef } from "react"
import { useLocation } from "react-router-dom"
import { MoreHorizontal, Edit, Trash2 } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { MediaCarousel } from "@/components/media/media-carousel"
import { CommentsSection } from "./comments-section"
import { PostReviewersSection } from "./post-reviewers-section"
import { PostReviewForm } from "./post-review-form"
import { useTranslations } from "@/lib/i18n/typed-translations"
import { usePost } from "@/hooks/posts"
import { useDeletePost } from "@/hooks/posts"
import { toast } from "sonner"
import { formatDistanceToNow } from "date-fns"
import type { MediaItem } from "@/lib/types/api"
import { cn } from '@/lib/utils.ts';
import { useIsMobile } from '@/hooks/use-mobile.ts';
import { DeepLinkUtils } from "@/lib/notification-navigation"

interface PostDialogProps {
  postId: number
  hubId: number
  open: boolean
  onOpenChange: (open: boolean) => void
  onEdit?: (postId: number) => void
  scrollToComments?: boolean
}

export function PostDialog({ postId, hubId, open, onOpenChange, onEdit, scrollToComments = false }: PostDialogProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const location = useLocation()
  const { t, keys } = useTranslations()
  const isMobile = useIsMobile()
  const { data: post, isLoading, error } = usePost(postId, { enabled: open })
  const deletePost = useDeletePost()

  // Simple dialog close handler - parent handles URL management
  const handleDialogClose = (newOpen: boolean) => {
    onOpenChange(newOpen)
  }

  // Scroll to comments section within the dialog's ScrollArea
  useEffect(() => {
    console.log('=== SCROLL EFFECT TRIGGERED ===')
    console.log('Conditions:', {
      open,
      scrollToComments,
      hash: location.hash,
      isLoading,
      post: !!post,
      hashMatch: location.hash === '#post-comments-section',
      commentIdMatch: DeepLinkUtils.getCommentToScrollTo(location.hash)
    })

    if (open && (scrollToComments || DeepLinkUtils.getCommentToScrollTo(location.hash) || location.hash === '#post-comments-section') && !isLoading && post) {
      console.log('✅ All scroll conditions met - starting scroll attempts')

      // Multiple attempts with increasing delays to handle different rendering scenarios
      const attemptScroll = (attempt = 1, maxAttempts = 5) => {
        const delay = attempt * 200 // 200ms, 400ms, 600ms, 800ms, 1000ms

        const timer = setTimeout(() => {
          console.log(`🔄 Scroll attempt ${attempt}/${maxAttempts}`)

          const commentsSection = document.getElementById('post-comments-section')

          if (!commentsSection) {
            console.log('❌ Comments section not found on attempt', attempt)
            if (attempt < maxAttempts) {
              console.log('⏳ Retrying...')
              attemptScroll(attempt + 1, maxAttempts)
            } else {
              console.log('💥 Max attempts reached - comments section never found')
            }
            return
          }

          console.log('✅ Comments section found:', commentsSection)
          console.log('Comments section details:', {
            id: commentsSection.id,
            offsetTop: commentsSection.offsetTop,
            offsetHeight: commentsSection.offsetHeight,
            scrollTop: commentsSection.scrollTop,
            clientHeight: commentsSection.clientHeight,
            parentElement: commentsSection.parentElement?.tagName
          })

          // Find the correct viewport within the dialog that contains our comments section
          let viewport: HTMLElement | null = null

          // First, find the dialog that contains our comments section
          const allDialogs = document.querySelectorAll('[role="dialog"]')
          console.log('Found dialogs:', allDialogs.length)

          let targetDialog: Element | null = null
          for (let i = 0; i < allDialogs.length; i++) {
            const dialog = allDialogs[i]
            if (dialog.contains(commentsSection)) {
              targetDialog = dialog
              console.log(`✅ Found target dialog (index ${i}) containing comments section`)
              break
            }
          }

          if (targetDialog) {
            // Now find the ScrollArea viewport within this specific dialog
            viewport = targetDialog.querySelector('[data-radix-scroll-area-viewport]') as HTMLElement
            console.log('Viewport found within target dialog:', !!viewport)
          } else {
            console.log('⚠️ No dialog contains the comments section, using first viewport')
            viewport = document.querySelector('[data-radix-scroll-area-viewport]') as HTMLElement
          }

          console.log('Viewport search results:', {
            viewport: !!viewport,
            viewportElement: viewport
          })

          if (viewport) {
            console.log('✅ Viewport found:', viewport)
            console.log('Viewport details:', {
              scrollTop: viewport.scrollTop,
              scrollHeight: viewport.scrollHeight,
              clientHeight: viewport.clientHeight,
              offsetHeight: viewport.offsetHeight,
              children: viewport.children.length
            })
          }

          if (viewport) {
            console.log('\n🎯 VIEWPORT FOUND - Starting scroll logic')

            // Find the scrollable content container
            const scrollableContent = viewport.children[0] as HTMLElement

            console.log('Scrollable content check:', {
              scrollableContent: !!scrollableContent,
              childrenCount: viewport.children.length,
              firstChild: viewport.children[0]
            })

            if (!scrollableContent) {
              console.log('❌ Scrollable content not found - aborting')
              return
            }

            console.log('✅ Scrollable content found:', scrollableContent)

            // Get initial measurements
            const initialScrollTop = viewport.scrollTop
            const viewportHeight = viewport.clientHeight
            const scrollHeight = viewport.scrollHeight

            console.log('Initial viewport measurements:', {
              initialScrollTop,
              viewportHeight,
              scrollHeight,
              canScroll: scrollHeight > viewportHeight
            })

            // Calculate the comments section's position relative to the scrollable content
            let commentsSectionOffsetTop = 0
            let currentElement: HTMLElement | null = commentsSection
            const offsetCalculationSteps: string[] = []

            // Walk up the DOM tree to calculate the total offset from the scrollable content
            while (currentElement && currentElement !== scrollableContent) {
              const elementOffset = currentElement.offsetTop
              commentsSectionOffsetTop += elementOffset
              offsetCalculationSteps.push(`${currentElement.tagName}#${currentElement.id || 'no-id'}: +${elementOffset}px`)
              currentElement = currentElement.offsetParent as HTMLElement

              // Safety check to prevent infinite loops
              if (currentElement === document.body || !scrollableContent.contains(currentElement)) {
                offsetCalculationSteps.push('BREAK: reached body or element not in scrollable content')
                break
              }
            }

            console.log('Offset calculation steps:', offsetCalculationSteps)
            console.log('Final commentsSectionOffsetTop:', commentsSectionOffsetTop)

            // Check if comments section is actually visible within the ScrollArea viewport
            const commentsSectionTop = commentsSectionOffsetTop - initialScrollTop
            const commentsSectionBottom = commentsSectionTop + commentsSection.offsetHeight

            const isActuallyVisible = commentsSectionTop >= 0 &&
                                    commentsSectionTop <= viewportHeight - 100 // Need at least 100px visible

            console.log('📏 Visibility calculation:', {
              commentsSectionOffsetTop,
              initialScrollTop,
              viewportHeight,
              commentsSectionTop: `${commentsSectionTop}px (${commentsSectionTop >= 0 ? 'below' : 'above'} viewport top)`,
              commentsSectionBottom: `${commentsSectionBottom}px`,
              commentsSectionHeight: commentsSection.offsetHeight,
              isActuallyVisible,
              visibilityReason: isActuallyVisible ? 'visible' : commentsSectionTop < 0 ? 'above viewport' : 'below viewport'
            })

            if (isActuallyVisible) {
              console.log('✅ Comments section is actually visible, no scroll needed')
              return
            }

            // Calculate target scroll position to show comments section near the top of viewport
            const targetScrollTop = Math.max(0, commentsSectionOffsetTop - 50) // 50px from top for better visibility

            console.log('\n🚀 EXECUTING SCROLL:')
            console.log('Scroll details:', {
              from: initialScrollTop,
              to: targetScrollTop,
              distance: targetScrollTop - initialScrollTop,
              direction: targetScrollTop > initialScrollTop ? 'down' : 'up'
            })

            // Store scroll position before scroll attempt
            const beforeScrollTop = viewport.scrollTop

            viewport.scrollTo({
              top: targetScrollTop,
              behavior: 'smooth'
            })

            console.log('📤 Scroll command sent')

            // Check if scroll actually happened after a short delay
            setTimeout(() => {
              const afterScrollTop = viewport.scrollTop
              const maxScrollTop = viewport.scrollHeight - viewport.clientHeight
              const scrolledToMax = Math.abs(afterScrollTop - maxScrollTop) < 10
              const scrollSuccess = Math.abs(afterScrollTop - targetScrollTop) < 50 || scrolledToMax

              console.log('📥 Final scroll result:', {
                beforeScrollTop,
                afterScrollTop,
                targetScrollTop,
                maxScrollTop,
                scrolledToMax,
                scrollSuccess,
                commentsVisiblePosition: commentsSectionOffsetTop - afterScrollTop
              })

              if (scrollSuccess) {
                console.log('✅ Scroll completed successfully!')
                console.log(`Comments section is now at position ${commentsSectionOffsetTop - afterScrollTop}px from top of viewport`)
              } else {
                console.log('⚠️ Scroll may not have completed properly')

                // Try direct scrollTop assignment as fallback
                const directScrollTarget = Math.min(targetScrollTop, maxScrollTop)
                viewport.scrollTop = directScrollTarget

                setTimeout(() => {
                  console.log('📋 Fallback scroll result:', {
                    finalScrollTop: viewport.scrollTop,
                    success: Math.abs(viewport.scrollTop - directScrollTarget) < 10
                  })
                }, 100)
              }
            }, 300) // Allow time for smooth scroll to complete
          } else {
            console.log('❌ VIEWPORT NOT FOUND - Using fallback scroll')
            console.log('Available scroll methods for comments section:')
            console.log('- scrollIntoView available:', typeof commentsSection.scrollIntoView === 'function')

            // Fallback: scroll the comments section into view
            console.log('🔄 Executing fallback scrollIntoView...')
            commentsSection.scrollIntoView({
              behavior: 'smooth',
              block: 'start',
              inline: 'nearest'
            })

            console.log('📤 Fallback scroll command sent')
          }
        }, delay)

        return timer
      }

      const timer = attemptScroll()
      return () => clearTimeout(timer)
    } else {
      console.log('❌ Scroll conditions not met - no scroll will be attempted')
    }
  }, [open, scrollToComments, isLoading, post, location.hash])

  const getInitials = (name?: string) => {
    if (!name) return '??'
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const formatRelativeTime = (dateString?: string) => {
    if (!dateString) return ''
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true })
    } catch {
      return ''
    }
  }

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800 border-green-200'
      case 'needs_rework': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'pending': return 'bg-blue-100 text-blue-800 border-blue-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const highlightHashtagsAndMentions = (text: string) => {
    return text
      .replace(/\n/g, '<br>')
      .replace(
        /(#[^\s]+|@[^\s]+)/g,
        '<span class="text-blue-600 hover:text-blue-700 font-medium cursor-pointer">$1</span>'
      )
  }

  const handleEdit = () => {
    if (post?.id) {
      onEdit?.(post.id)
      handleDialogClose(false)
    }
  }

  const handleDeleteConfirm = async () => {
    if (!post?.id) return

    try {
      await deletePost.mutateAsync({ params: { path: { postId: post.id } } })
      toast.success(t(keys.collaborationHubs.posts.postDeleted))
      setDeleteDialogOpen(false)
      handleDialogClose(false)
    } catch (error) {
      console.error('Failed to delete post:', error)
      toast.error(t(keys.collaborationHubs.posts.failedToDelete))
    }
  }

  // Convert API MediaItem to MediaCarousel format
  const convertMediaItems = (mediaItems?: MediaItem[]) => {
    if (!mediaItems || mediaItems.length === 0) return []

    return mediaItems.map((item, index) => ({
      id: `media-${index}`,
      type: (item.type === 'video' ? 'video' : 'image') as 'image' | 'video',
      url: item.url || '',
      alt: `Media ${index + 1}`
    }))
  }

  if (error) {
    return (
      <Dialog open={open} onOpenChange={handleDialogClose}>
        <DialogContent className="max-w-4xl max-h-[90vh]">
          <div className="flex items-center justify-center py-8">
            <p className="text-muted-foreground">Failed to load post</p>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <>
      <Dialog open={open} onOpenChange={handleDialogClose}>
        <DialogContent className={cn(
          "max-w-4xl",
          isMobile && "h-[100dvh] max-h-[100dvh] w-[100vw] max-w-[100vw] rounded-none p-0"
        )}>
          <DialogHeader className={cn(
            isMobile && "pb-2 pt-4 px-4"
          )}>
            <DialogTitle className={cn(
              "text-lg font-semibold",
              isMobile && "text-base"
            )}>
              {t(keys.collaborationHubs.posts.title)}
            </DialogTitle>
          </DialogHeader>

          <ScrollArea
            className={cn(
              "mt-4",
              isMobile ? "h-[calc(100dvh-80px)] px-4" : "max-h-[75vh]"
            )}>
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : post ? (
              <div className={cn(
                "space-y-6",
                isMobile && "pb-4"
              )}>
                  {/* Post Header */}
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={undefined} />
                        <AvatarFallback>
                          {getInitials(post.creator?.name)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-semibold">{post.creator?.name || 'Unknown'}</span>
                          {post.review_status && (
                            <Badge variant="outline" className={getStatusColor(post.review_status)}>
                              {post.review_status.replace('_', ' ')}
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {formatRelativeTime(post.created_at)}
                        </p>
                      </div>
                    </div>

                    {/* Actions Menu */}
                    {(post.permissions?.can_edit) && (
                      <DropdownMenu>
                        <DropdownMenuTrigger>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          {post.permissions?.can_edit && (
                            <DropdownMenuItem onClick={handleEdit}>
                              <Edit className="h-4 w-4 mr-2" />
                              {t(keys.collaborationHubs.posts.actions.edit)}
                            </DropdownMenuItem>
                          )}
                          {post.permissions?.can_edit && (
                            <DropdownMenuItem
                              onClick={() => setDeleteDialogOpen(true)}
                              className="text-destructive focus:text-destructive"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              {t(keys.collaborationHubs.posts.actions.delete)}
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )}
                  </div>

                  {/* Media */}
                  {post.media_uris && post.media_uris.length > 0 && (
                    <div className="rounded-lg overflow-hidden">
                      <MediaCarousel
                        media={convertMediaItems(post.media_uris)}
                        hubId={hubId}
                        postId={post.id}
                        className="h-[400px] md:h-[500px] lg:h-[600px]"
                        enableRetry={true}
                      />
                    </div>
                  )}

                  {/* Caption */}
                  {post.caption && (
                    <div
                      className="text-sm leading-relaxed"
                      dangerouslySetInnerHTML={{
                        __html: highlightHashtagsAndMentions(post.caption)
                      }}
                    />
                  )}

                  {/* Reviewer Notes */}
                  {post.reviewer_notes && (
                    <div className="bg-muted/50 rounded-lg p-4">
                      <h4 className="font-medium text-sm mb-2">Notes for Reviewers</h4>
                      <p className="text-sm text-muted-foreground whitespace-pre-wrap">{post.reviewer_notes}</p>
                    </div>
                  )}

                  {/* Enhanced Assigned Reviewers */}
                  {post.assigned_reviewers && post.assigned_reviewers.length > 0 && (
                    <div className="border rounded-lg p-4">
                      <PostReviewersSection
                        reviewers={post.assigned_reviewers}
                        showTimeline={true}
                      />
                    </div>
                  )}

                  {/* Post Review Form */}
                  {post.permissions.can_review && (
                    <PostReviewForm post={post} />
                  )}

                  {/* Comments Section */}
                  <div id="post-comments-section">
                    <CommentsSection
                      postId={postId}
                      hubId={hubId}
                      commentCount={0}
                      canComment={post.permissions?.can_comment || false}
                      className="border-t pt-6"
                      defaultExpanded={true}
                    />
                  </div>
              </div>
            ) : null}
          </ScrollArea>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t(keys.collaborationHubs.posts.confirmDelete)}</AlertDialogTitle>
            <AlertDialogDescription>
              {t(keys.collaborationHubs.posts.deleteDescription)}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t(keys.collaborationHubs.posts.form.cancel)}</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDeleteConfirm}
              disabled={deletePost.isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {deletePost.isPending ? t(keys.collaborationHubs.posts.deleting) : t(keys.collaborationHubs.posts.actions.delete)}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
