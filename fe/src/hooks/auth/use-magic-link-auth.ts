import { $api } from '@/lib/api/client';
import { useAuth } from '@/contexts/auth-context';
import { useNavigate } from 'react-router-dom';

/**
 * Custom hook for magic link authentication functionality.
 * Handles external participant authentication via magic links with automatic redirect
 * based on the redirect context returned from the backend.
 */
export function useMagicLinkAuth() {
  const { updateAuthState } = useAuth();
  const navigate = useNavigate();

  const { mutate, mutateAsync, isPending, error, isError, isSuccess, reset } = $api.useMutation(
    'post',
    '/api/auth/magic-link/authenticate',
    {
      onSuccess: (data) => {
        // Update auth state with the authentication response
        updateAuthState(data);

        // Handle automatic redirect based on redirect context
        if (data.redirect_context) {
          const targetUrl = getTargetUrlFromRedirectContext(data.redirect_context);
          if (targetUrl) {
            navigate(targetUrl, { replace: true });
            return;
          }
        }

        // Fallback redirect to dashboard if no redirect context
        navigate('/app/dashboard', { replace: true });
      },
    }
  );

  return {
    authenticateWithMagicLink: mutate,
    authenticateWithMagicLinkAsync: mutateAsync,
    isPending,
    error,
    isError,
    isSuccess,
    reset,
  };
}

// Simple type for redirect context - using any to avoid complex polymorphic typing
// The backend returns different structures based on the type field



/**
 * Extracts the target URL from redirect context.
 */
function getTargetUrlFromRedirectContext(redirectContext: any): string | null {
  if (!redirectContext) return null;

  // Handle different redirect types
  switch (redirectContext.type) {
    case 'HUB_ACCESS':
      return redirectContext.hubId ? `/app/collaboration-hubs/${redirectContext.hubId}` : null;
    case 'INVOICE_ACCESS':
      return redirectContext.invoiceId ? `/app/invoices/${redirectContext.invoiceId}` : null;
    case 'GENERAL_ACCESS':
      return redirectContext.defaultPage || '/app/dashboard';
    default:
      return null;
  }
}


