package com.collabhub.be.modules.notifications.controller;

import com.collabhub.be.modules.notifications.dto.MarkNotificationReadRequest;
import com.collabhub.be.modules.notifications.dto.NotificationPageRequest;
import com.collabhub.be.modules.notifications.dto.NotificationPageResponse;
import com.collabhub.be.modules.notifications.dto.NotificationResponse;
import com.collabhub.be.modules.notifications.service.NotificationManagementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * REST controller for in-app notification operations.
 * Handles notification retrieval, status updates, and unread counts.
 */
@RestController
@RequestMapping("/api/notifications")
@Tag(name = "Notifications", description = "In-app notification management")
public class NotificationController {

    private static final Logger logger = LoggerFactory.getLogger(NotificationController.class);

    private final NotificationManagementService notificationManagementService;

    public NotificationController(NotificationManagementService notificationManagementService) {
        this.notificationManagementService = notificationManagementService;
    }

    /**
     * Retrieves notifications for the current user with pagination.
     *
     * @param page the page number (0-based)
     * @param size the page size
     * @param unreadOnly whether to return only unread notifications
     * @return page of notifications
     */
    @GetMapping
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Get notifications", description = "Retrieves notifications for the current user with pagination")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Notifications retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Authentication required")
    })
    public ResponseEntity<NotificationPageResponse> getNotifications(
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "Return only unread notifications") @RequestParam(defaultValue = "false") boolean unreadOnly) {

        logger.debug("Retrieving notifications: page={}, size={}, unreadOnly={}", page, size, unreadOnly);

        NotificationPageRequest pageRequest = NotificationPageRequest.of(page, size, unreadOnly);
        NotificationPageResponse notifications = notificationManagementService.getUserNotifications(pageRequest);

        logger.info("Successfully retrieved {} notifications (page {}/{})",
                   notifications.getNumberOfElements(), page + 1, notifications.getTotalPages());

        return ResponseEntity.ok(notifications);
    }

    /**
     * Marks a specific notification as read.
     *
     * @param request the mark as read request
     * @return success response
     */
    @PostMapping("/mark-read")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Mark notification as read", description = "Marks a specific notification as read")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Notification marked as read successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request data"),
            @ApiResponse(responseCode = "401", description = "Authentication required"),
            @ApiResponse(responseCode = "404", description = "Notification not found or not owned by user")
    })
    public ResponseEntity<Void> markNotificationAsRead(@Valid @RequestBody MarkNotificationReadRequest request) {
        logger.debug("Marking notification as read: id={}", request.getNotificationId());

        notificationManagementService.markNotificationAsRead(request.getNotificationId());

        logger.info("Successfully marked notification as read: id={}", request.getNotificationId());
        return ResponseEntity.ok().build();
    }

    /**
     * Marks all notifications as read for the current user.
     *
     * @return number of notifications marked as read
     */
    @PostMapping("/mark-all-read")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Mark all notifications as read", description = "Marks all notifications as read for the current user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "All notifications marked as read successfully"),
            @ApiResponse(responseCode = "401", description = "Authentication required")
    })
    public ResponseEntity<Integer> markAllNotificationsAsRead() {
        logger.debug("Marking all notifications as read for current user");

        int updatedCount = notificationManagementService.markAllNotificationsAsRead();

        logger.info("Successfully marked {} notifications as read", updatedCount);
        return ResponseEntity.ok(updatedCount);
    }

    /**
     * Gets the count of unread notifications for the current user.
     *
     * @return number of unread notifications
     */
    @GetMapping("/unread-count")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Get unread notification count", description = "Gets the count of unread notifications for the current user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Unread count retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Authentication required")
    })
    public ResponseEntity<Integer> getUnreadNotificationCount() {
        logger.debug("Getting unread notification count for current user");

        int count = notificationManagementService.getUnreadNotificationCount();

        logger.info("Current user has {} unread notifications", count);
        return ResponseEntity.ok(count);
    }
}
