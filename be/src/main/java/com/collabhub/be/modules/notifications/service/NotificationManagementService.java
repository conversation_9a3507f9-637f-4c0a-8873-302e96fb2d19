package com.collabhub.be.modules.notifications.service;

import com.collabhub.be.modules.auth.service.JwtClaimsService;
import com.collabhub.be.modules.notifications.constants.NotificationConstants;
import com.collabhub.be.modules.notifications.converter.NotificationConverter;
import com.collabhub.be.modules.notifications.dto.NotificationPageRequest;
import com.collabhub.be.modules.notifications.dto.NotificationPageResponse;
import com.collabhub.be.modules.notifications.dto.NotificationResponse;
import com.collabhub.be.modules.notifications.repository.NotificationRepository;
import org.jooq.generated.tables.pojos.Notification;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * Service for managing user notifications in the application.
 * Handles retrieval, status updates, and pagination for in-app notifications.
 */
@Service
@Validated
public class NotificationManagementService {

    private static final Logger logger = LoggerFactory.getLogger(NotificationManagementService.class);

    private final NotificationRepository notificationRepository;
    private final NotificationConverter notificationConverter;
    private final JwtClaimsService jwtClaimsService;

    public NotificationManagementService(NotificationRepository notificationRepository,
                                       NotificationConverter notificationConverter,
                                       JwtClaimsService jwtClaimsService) {
        this.notificationRepository = notificationRepository;
        this.notificationConverter = notificationConverter;
        this.jwtClaimsService = jwtClaimsService;
    }

    /**
     * Retrieves notifications for the current user with pagination.
     *
     * @param pageRequest the page request (0-based pagination)
     * @return page of notifications
     */
    @Transactional(readOnly = true)
    public NotificationPageResponse getUserNotifications(NotificationPageRequest pageRequest) {
        Long userId = jwtClaimsService.getCurrentUser().getUserId();

        logger.debug("Retrieving notifications for user: {} (page={}, size={}, unreadOnly={})",
                    userId, pageRequest.getPage(), pageRequest.getSize(), pageRequest.isUnreadOnly());

        // Get notifications with pagination
        List<Notification> notifications = notificationRepository.findByUserIdWithPagination(
                userId, pageRequest.getPage(), pageRequest.getSize(), pageRequest.isUnreadOnly());

        // Get total count for pagination
        int totalCount = notificationRepository.countByUserId(userId, pageRequest.isUnreadOnly());

        // Convert to response DTOs
        List<NotificationResponse> responseList = notificationConverter.toResponseList(notifications);

        logger.info("Retrieved {} notifications for user {} (total: {})",
                   responseList.size(), userId, totalCount);

        return NotificationPageResponse.of(responseList, pageRequest, totalCount);
    }

    /**
     * Marks a specific notification as read.
     *
     * @param notificationId the notification ID
     */
    @Transactional
    public void markNotificationAsRead(Long notificationId) {
        Long userId = jwtClaimsService.getCurrentUser().getUserId();
        
        logger.debug("Marking notification as read: id={}, user={}", notificationId, userId);

        boolean updated = notificationRepository.markAsRead(notificationId, userId);
        
        if (updated) {
            logger.info("Marked notification as read: id={}, user={}", notificationId, userId);
        } else {
            logger.warn("Failed to mark notification as read - not found or not owned: id={}, user={}", 
                       notificationId, userId);
        }
    }

    /**
     * Marks all notifications as read for the current user.
     *
     * @return number of notifications marked as read
     */
    @Transactional
    public int markAllNotificationsAsRead() {
        Long userId = jwtClaimsService.getCurrentUser().getUserId();
        
        logger.debug("Marking all notifications as read for user: {}", userId);

        int updatedCount = notificationRepository.markAllAsRead(userId);
        
        logger.info("Marked {} notifications as read for user {}", updatedCount, userId);
        return updatedCount;
    }

    /**
     * Gets the count of unread notifications for the current user.
     *
     * @return number of unread notifications
     */
    @Transactional(readOnly = true)
    public int getUnreadNotificationCount() {
        Long userId = jwtClaimsService.getCurrentUser().getUserId();
        
        logger.debug("Getting unread notification count for user: {}", userId);

        int count = notificationRepository.countByUserId(userId, true);
        
        logger.debug("User {} has {} unread notifications", userId, count);
        return count;
    }
}
