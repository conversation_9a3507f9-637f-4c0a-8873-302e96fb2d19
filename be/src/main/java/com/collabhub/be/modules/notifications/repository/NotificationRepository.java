package com.collabhub.be.modules.notifications.repository;

import com.collabhub.be.modules.notifications.dto.NotificationStatus;
import org.jooq.DSLContext;
import org.jooq.generated.tables.daos.NotificationDao;
import org.jooq.generated.tables.pojos.Notification;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

import static org.jooq.generated.Tables.NOTIFICATION;

/**
 * Repository for Notification entity using jOOQ for database operations.
 * Provides multi-tenant aware queries scoped by user_id.
 */
@Repository
public class NotificationRepository extends NotificationDao {

    private final DSLContext dsl;

    public NotificationRepository(DSLContext dsl) {
        super(dsl.configuration());
        this.dsl = dsl;
    }

    /**
     * Finds notifications for a user with pagination.
     *
     * @param userId the user ID
     * @param page the page number (0-based)
     * @param size the page size
     * @param unreadOnly whether to return only unread notifications
     * @return list of notifications
     */
    public List<Notification> findByUserIdWithPagination(Long userId, int page, int size, boolean unreadOnly) {
        var query = dsl.select()
                .from(NOTIFICATION)
                .where(NOTIFICATION.USER_ID.eq(userId));

        if (unreadOnly) {
            query = query.and(NOTIFICATION.STATUS.eq(NotificationStatus.UNREAD.toJooqEnum()));
        }

        return query.orderBy(NOTIFICATION.CREATED_AT.desc())
                .limit(size)
                .offset(page * size)
                .fetchInto(Notification.class);
    }

    /**
     * Counts total notifications for a user.
     *
     * @param userId the user ID
     * @param unreadOnly whether to count only unread notifications
     * @return total count
     */
    public int countByUserId(Long userId, boolean unreadOnly) {
        var query = dsl.selectCount()
                .from(NOTIFICATION)
                .where(NOTIFICATION.USER_ID.eq(userId));

        if (unreadOnly) {
            query = query.and(NOTIFICATION.STATUS.eq(NotificationStatus.UNREAD.toJooqEnum()));
        }

        return query.fetchOneInto(Integer.class);
    }

    /**
     * Marks a specific notification as read.
     *
     * @param notificationId the notification ID
     * @param userId the user ID (for security)
     * @return true if updated, false if not found or not owned by user
     */
    public boolean markAsRead(Long notificationId, Long userId) {
        LocalDateTime now = LocalDateTime.now();
        
        int updatedRows = dsl.update(NOTIFICATION)
                .set(NOTIFICATION.STATUS, NotificationStatus.READ.toJooqEnum())
                .set(NOTIFICATION.READ_AT, now)
                .where(NOTIFICATION.ID.eq(notificationId))
                .and(NOTIFICATION.USER_ID.eq(userId))
                .and(NOTIFICATION.STATUS.eq(NotificationStatus.UNREAD.toJooqEnum()))
                .execute();

        return updatedRows > 0;
    }

    /**
     * Marks all notifications as read for a user.
     *
     * @param userId the user ID
     * @return number of notifications updated
     */
    public int markAllAsRead(Long userId) {
        LocalDateTime now = LocalDateTime.now();
        
        return dsl.update(NOTIFICATION)
                .set(NOTIFICATION.STATUS, NotificationStatus.READ.toJooqEnum())
                .set(NOTIFICATION.READ_AT, now)
                .where(NOTIFICATION.USER_ID.eq(userId))
                .and(NOTIFICATION.STATUS.eq(NotificationStatus.UNREAD.toJooqEnum()))
                .execute();
    }

    /**
     * Deletes old notifications to prevent database bloat.
     *
     * @param cutoffDate notifications older than this date will be deleted
     * @param batchSize maximum number of notifications to delete in one operation
     * @return number of notifications deleted
     */
    public int deleteOldNotifications(LocalDateTime cutoffDate, int batchSize) {
        return dsl.deleteFrom(NOTIFICATION)
                .where(NOTIFICATION.CREATED_AT.lt(cutoffDate))
                .limit(batchSize)
                .execute();
    }
}
