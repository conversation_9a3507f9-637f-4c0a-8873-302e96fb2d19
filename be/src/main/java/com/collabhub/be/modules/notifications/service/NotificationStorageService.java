package com.collabhub.be.modules.notifications.service;

import com.collabhub.be.modules.auth.service.JwtClaimsService;
import com.collabhub.be.modules.notifications.constants.NotificationConstants;
import com.collabhub.be.modules.notifications.dto.NotificationStatus;
import com.collabhub.be.modules.notifications.dto.NotificationType;
import com.collabhub.be.modules.notifications.repository.NotificationRepository;
import org.jooq.JSONB;
import org.jooq.generated.tables.pojos.Notification;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Service for storing and managing in-app notifications.
 * Handles notification persistence, status updates, and cleanup.
 */
@Service
public class NotificationStorageService {

    private static final Logger logger = LoggerFactory.getLogger(NotificationStorageService.class);

    // Cleanup constants
    private static final int NOTIFICATION_RETENTION_DAYS = 30;
    private static final int CLEANUP_BATCH_SIZE = 1000;

    private final NotificationRepository notificationRepository;
    private final JwtClaimsService jwtClaimsService;

    public NotificationStorageService(NotificationRepository notificationRepository,
                                    JwtClaimsService jwtClaimsService) {
        this.notificationRepository = notificationRepository;
        this.jwtClaimsService = jwtClaimsService;
    }

    /**
     * Creates in-app notifications for multiple users.
     *
     * @param type the notification type
     * @param title the notification title
     * @param message the notification message
     * @param recipientUserIds the user IDs to create notifications for
     * @param entityReferences optional entity references (hubId, postId, etc.)
     * @param metadata optional additional metadata
     */
    @Transactional
    public void createNotifications(NotificationType type, String title, String message,
                                  Set<Long> recipientUserIds, EntityReferences entityReferences,
                                  Map<String, Object> metadata) {
        
        logger.debug("Creating {} notifications of type {} for {} users", 
                    recipientUserIds.size(), type, recipientUserIds.size());

        for (Long userId : recipientUserIds) {
            Notification notification = createSingleNotification(
                    userId, type, title, message, entityReferences, metadata);
            
            notificationRepository.insert(notification);
            
            logger.debug("Created notification: user={}, type={}, title={}", 
                        userId, type, title);
        }

        logger.info("Successfully created {} notifications of type {}", 
                   recipientUserIds.size(), type);
    }

    /**
     * Retrieves notifications for the current user with pagination.
     *
     * @param page the page number (0-based)
     * @param size the page size
     * @param unreadOnly whether to return only unread notifications
     * @return list of notifications
     */
    @Transactional(readOnly = true)
    public List<Notification> getUserNotifications(int page, int size, boolean unreadOnly) {
        Long userId = jwtClaimsService.getCurrentUser().getUserId();
        
        logger.debug("Retrieving notifications for user: {} (page={}, size={}, unreadOnly={})", 
                    userId, page, size, unreadOnly);

        List<Notification> notifications = notificationRepository.findByUserIdWithPagination(
                userId, page, size, unreadOnly);

        logger.info("Retrieved {} notifications for user {}", notifications.size(), userId);
        return notifications;
    }

    /**
     * Marks a notification as read.
     *
     * @param notificationId the notification ID
     */
    @Transactional
    public void markAsRead(Long notificationId) {
        Long userId = jwtClaimsService.getCurrentUser().getUserId();
        
        logger.debug("Marking notification as read: id={}, user={}", notificationId, userId);

        boolean updated = notificationRepository.markAsRead(notificationId, userId);
        
        if (updated) {
            logger.info("Marked notification as read: id={}, user={}", notificationId, userId);
        } else {
            logger.warn("Failed to mark notification as read: id={}, user={}", notificationId, userId);
        }
    }

    /**
     * Marks all notifications as read for the current user.
     */
    @Transactional
    public void markAllAsRead() {
        Long userId = jwtClaimsService.getCurrentUser().getUserId();
        
        logger.debug("Marking all notifications as read for user: {}", userId);

        int updatedCount = notificationRepository.markAllAsRead(userId);
        
        logger.info("Marked {} notifications as read for user {}", updatedCount, userId);
    }

    /**
     * Cleans up old notifications to prevent database bloat.
     * Should be called periodically (e.g., daily cron job).
     */
    @Transactional
    public void cleanupOldNotifications() {
        logger.debug("Starting cleanup of old notifications (older than {} days)", NOTIFICATION_RETENTION_DAYS);

        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(NOTIFICATION_RETENTION_DAYS);
        int deletedCount = notificationRepository.deleteOldNotifications(cutoffDate, CLEANUP_BATCH_SIZE);

        logger.info("Cleaned up {} old notifications", deletedCount);
    }

    /**
     * Creates a single notification entity.
     */
    private Notification createSingleNotification(Long userId, NotificationType type, String title, 
                                                String message, EntityReferences entityReferences,
                                                Map<String, Object> metadata) {
        Notification notification = new Notification();
        notification.setUserId(userId);
        notification.setType(type.toJooqEnum());
        notification.setTitle(title);
        notification.setMessage(message);
        notification.setStatus(NotificationStatus.UNREAD.toJooqEnum());
        notification.setCreatedAt(LocalDateTime.now());

        // Set entity references if provided
        if (entityReferences != null) {
            notification.setCollaborationHubId(entityReferences.getHubId());
            notification.setPostId(entityReferences.getPostId());
            notification.setCommentId(entityReferences.getCommentId());
            notification.setChatChannelId(entityReferences.getChatChannelId());
            notification.setBriefId(entityReferences.getBriefId());
        }

        // Set metadata if provided
        if (metadata != null && !metadata.isEmpty()) {
            // Convert metadata to JSONB format (implementation depends on jOOQ JSONB support)
            // For now, we'll store it as a simple JSON string
            notification.setMetadata(convertMetadataToJsonb(metadata));
        }

        return notification;
    }

    /**
     * Converts metadata map to JSONB format.
     * This is a placeholder - actual implementation depends on jOOQ JSONB support.
     */
    private JSONB convertMetadataToJsonb(Map<String, Object> metadata) {
        // TODO: Implement proper JSONB conversion based on jOOQ configuration
        // For now, return null to avoid compilation errors
        return null;
    }

    /**
     * Data class for entity references in notifications.
     */
    public static class EntityReferences {
        private Long hubId;
        private Long postId;
        private Long commentId;
        private Long chatChannelId;
        private Long briefId;

        public EntityReferences() {}

        public static EntityReferences hub(Long hubId) {
            EntityReferences refs = new EntityReferences();
            refs.hubId = hubId;
            return refs;
        }

        public static EntityReferences post(Long hubId, Long postId) {
            EntityReferences refs = new EntityReferences();
            refs.hubId = hubId;
            refs.postId = postId;
            return refs;
        }

        public static EntityReferences comment(Long hubId, Long postId, Long commentId) {
            EntityReferences refs = new EntityReferences();
            refs.hubId = hubId;
            refs.postId = postId;
            refs.commentId = commentId;
            return refs;
        }

        public static EntityReferences chat(Long hubId, Long chatChannelId) {
            EntityReferences refs = new EntityReferences();
            refs.hubId = hubId;
            refs.chatChannelId = chatChannelId;
            return refs;
        }

        public static EntityReferences brief(Long hubId, Long briefId) {
            EntityReferences refs = new EntityReferences();
            refs.hubId = hubId;
            refs.briefId = briefId;
            return refs;
        }

        // Legacy methods for backward compatibility (deprecated)
        @Deprecated
        public static EntityReferences post(Long postId) {
            EntityReferences refs = new EntityReferences();
            refs.postId = postId;
            return refs;
        }

        @Deprecated
        public static EntityReferences comment(Long commentId) {
            EntityReferences refs = new EntityReferences();
            refs.commentId = commentId;
            return refs;
        }

        @Deprecated
        public static EntityReferences chat(Long chatChannelId) {
            EntityReferences refs = new EntityReferences();
            refs.chatChannelId = chatChannelId;
            return refs;
        }

        @Deprecated
        public static EntityReferences brief(Long briefId) {
            EntityReferences refs = new EntityReferences();
            refs.briefId = briefId;
            return refs;
        }

        // Getters and setters
        public Long getHubId() { return hubId; }
        public void setHubId(Long hubId) { this.hubId = hubId; }
        
        public Long getPostId() { return postId; }
        public void setPostId(Long postId) { this.postId = postId; }
        
        public Long getCommentId() { return commentId; }
        public void setCommentId(Long commentId) { this.commentId = commentId; }
        
        public Long getChatChannelId() { return chatChannelId; }
        public void setChatChannelId(Long chatChannelId) { this.chatChannelId = chatChannelId; }

        public Long getBriefId() { return briefId; }
        public void setBriefId(Long briefId) { this.briefId = briefId; }
    }
}
