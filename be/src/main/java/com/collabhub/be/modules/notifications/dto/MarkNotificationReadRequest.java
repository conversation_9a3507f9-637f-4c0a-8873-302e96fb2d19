package com.collabhub.be.modules.notifications.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

/**
 * Request DTO for marking a notification as read.
 */
@Schema(description = "Request to mark a notification as read")
public class MarkNotificationReadRequest {

    @NotNull
    @Schema(description = "Notification ID to mark as read", example = "123")
    private Long notificationId;

    // Constructors
    public MarkNotificationReadRequest() {}

    public MarkNotificationReadRequest(Long notificationId) {
        this.notificationId = notificationId;
    }

    // Getters and setters
    public Long getNotificationId() {
        return notificationId;
    }

    public void setNotificationId(Long notificationId) {
        this.notificationId = notificationId;
    }
}
