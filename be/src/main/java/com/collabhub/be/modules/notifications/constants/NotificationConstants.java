package com.collabhub.be.modules.notifications.constants;

/**
 * Constants for notification-related operations.
 */
public final class NotificationConstants {

    private NotificationConstants() {
        // Utility class - prevent instantiation
    }

    // Default Settings
    public static final boolean DEFAULT_NOTIFICATION_ENABLED = true;

    // Validation Constants
    public static final int MAX_PREFERENCES_PER_REQUEST = 50;

    // Error Messages
    public static final String INVALID_CHANNEL_FOR_EXTERNAL_USER = "External users can only use EMAIL channel";
    public static final String PREFERENCE_NOT_FOUND = "Notification preference not found";
    public static final String USER_NOT_FOUND = "User not found";

    // Logging Messages
    public static final String PREFERENCES_RETRIEVED_LOG = "Retrieved {} notification preferences for user {}";
    public static final String PREFERENCES_UPDATED_LOG = "Updated {} notification preferences for user {}";
    public static final String PREFERENCE_CREATED_LOG = "Created notification preference: user={}, type={}, channel={}, enabled={}";
    public static final String PREFERENCE_UPDATED_LOG = "Updated notification preference: user={}, type={}, channel={}, enabled={}";

    // Method Size Constants
    public static final int MAX_METHOD_LINES = 30;
    public static final int RECOMMENDED_METHOD_LINES = 20;
}
