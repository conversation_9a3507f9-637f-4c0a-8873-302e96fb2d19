package com.collabhub.be.modules.notifications.service;

import com.collabhub.be.modules.notifications.constants.NotificationConstants;
import com.collabhub.be.modules.notifications.dto.NotificationChannel;
import com.collabhub.be.modules.notifications.dto.NotificationType;
import com.collabhub.be.modules.notifications.repository.NotificationPreferenceRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;

/**
 * Service for handling notification events and determining delivery channels.
 * Maps domain events to notifications and filters based on user preferences.
 */
@Service
public class NotificationService {

    private static final Logger logger = LoggerFactory.getLogger(NotificationService.class);

    private final NotificationPreferenceRepository preferenceRepository;

    public NotificationService(NotificationPreferenceRepository preferenceRepository) {
        this.preferenceRepository = preferenceRepository;
    }

    /**
     * Processes a notification event and determines which users should receive it and through which channels.
     *
     * @param type the notification type
     * @param recipientUserIds the list of user IDs who should potentially receive the notification
     * @return notification delivery plan with user IDs grouped by channel
     */
    @Transactional(readOnly = true)
    public NotificationDeliveryPlan processNotificationEvent(NotificationType type, List<Long> recipientUserIds) {
        logger.debug("Processing notification event: type={}, recipients={}", type, recipientUserIds.size());

        NotificationDeliveryPlan deliveryPlan = new NotificationDeliveryPlan();

        for (Long userId : recipientUserIds) {
            // Check IN_APP preference
            Boolean inAppEnabled = preferenceRepository.isNotificationEnabled(userId, type, NotificationChannel.IN_APP);
            if (shouldDeliverNotification(inAppEnabled)) {
                deliveryPlan.addInAppRecipient(userId);
            }

            // Check EMAIL preference
            Boolean emailEnabled = preferenceRepository.isNotificationEnabled(userId, type, NotificationChannel.EMAIL);
            if (shouldDeliverNotification(emailEnabled)) {
                deliveryPlan.addEmailRecipient(userId);
            }
        }

        logger.info("Notification delivery plan: type={}, inApp={}, email={}", 
                   type, deliveryPlan.getInAppRecipients().size(), deliveryPlan.getEmailRecipients().size());

        return deliveryPlan;
    }

    /**
     * Determines if a notification should be delivered based on preference.
     * Defaults to enabled if no preference is set.
     */
    private boolean shouldDeliverNotification(Boolean preferenceEnabled) {
        return preferenceEnabled == null || preferenceEnabled;
    }

    /**
     * Data class representing a notification delivery plan.
     * Contains user IDs grouped by delivery channel.
     */
    public static class NotificationDeliveryPlan {
        private final Set<Long> inAppRecipients = new java.util.HashSet<>();
        private final Set<Long> emailRecipients = new java.util.HashSet<>();

        public void addInAppRecipient(Long userId) {
            inAppRecipients.add(userId);
        }

        public void addEmailRecipient(Long userId) {
            emailRecipients.add(userId);
        }

        public Set<Long> getInAppRecipients() {
            return inAppRecipients;
        }

        public Set<Long> getEmailRecipients() {
            return emailRecipients;
        }

        public boolean hasInAppRecipients() {
            return !inAppRecipients.isEmpty();
        }

        public boolean hasEmailRecipients() {
            return !emailRecipients.isEmpty();
        }
    }
}
