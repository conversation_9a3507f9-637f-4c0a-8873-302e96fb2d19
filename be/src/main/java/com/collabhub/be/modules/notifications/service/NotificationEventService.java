package com.collabhub.be.modules.notifications.service;

import com.collabhub.be.modules.notifications.constants.NotificationConstants;
import com.collabhub.be.modules.notifications.dto.NotificationType;
import com.collabhub.be.modules.auth.service.EmailService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;

/**
 * Service for handling domain events that trigger notifications.
 * Provides high-level methods for common notification scenarios.
 */
@Service
public class NotificationEventService {

    private static final Logger logger = LoggerFactory.getLogger(NotificationEventService.class);

    private final NotificationDispatcherService notificationDispatcherService;
    private final EmailService emailService;
    private final NotificationTranslationService translationService;

    public NotificationEventService(NotificationDispatcherService notificationDispatcherService,
                                  EmailService emailService,
                                  NotificationTranslationService translationService) {
        this.notificationDispatcherService = notificationDispatcherService;
        this.emailService = emailService;
        this.translationService = translationService;
    }

    /**
     * Handles user invitation to collaboration hub event.
     *
     * @param invitedUserId the user being invited
     * @param hubId the collaboration hub ID
     * @param hubName the collaboration hub name
     * @param inviterName the name of the person sending the invitation
     */
    @Transactional
    public void handleUserInvitedToHub(Long invitedUserId, Long hubId, String hubName, String inviterName) {
        logger.debug("Processing hub invitation: user={}, hub={}", invitedUserId, hubId);

        String title = "Invitation to Collaboration Hub";
        String message = String.format("%s invited you to join the collaboration hub '%s'", inviterName, hubName);

        NotificationStorageService.EntityReferences entityRefs = NotificationStorageService.EntityReferences.hub(hubId);
        Map<String, Object> metadata = Map.of(
                "inviter_name", inviterName,
                "hub_name", hubName
        );

        notificationDispatcherService.dispatchNotification(
                NotificationType.INVITE_TO_HUB, title, message, List.of(invitedUserId), entityRefs, metadata);

        logger.info("Processed hub invitation notification: user={}, hub={}", invitedUserId, hubId);
    }

    /**
     * Handles user assigned as reviewer event.
     *
     * @param reviewerUserId the user assigned as reviewer
     * @param hubId the collaboration hub ID
     * @param postId the post ID
     * @param postTitle the post title
     * @param assignerName the name of the person making the assignment
     */
    @Transactional
    public void handleUserAssignedAsReviewer(Long reviewerUserId, Long hubId, Long postId, String postTitle, String assignerName) {
        logger.debug("Processing reviewer assignment: user={}, hub={}, post={}", reviewerUserId, hubId, postId);

        String title = "Assigned as Reviewer";
        String message = String.format("%s assigned you to review the post '%s'", assignerName, postTitle);

        NotificationStorageService.EntityReferences entityRefs = NotificationStorageService.EntityReferences.post(hubId, postId);
        Map<String, Object> metadata = Map.of(
                "assigner_name", assignerName,
                "post_title", postTitle
        );

        notificationDispatcherService.dispatchNotification(
                NotificationType.ASSIGNED_AS_REVIEWER, title, message, List.of(reviewerUserId), entityRefs, metadata);

        logger.info("Processed reviewer assignment notification: user={}, hub={}, post={}", reviewerUserId, hubId, postId);
    }

    /**
     * Handles post reviewed event.
     *
     * @param postCreatorUserId the post creator user ID
     * @param hubId the collaboration hub ID
     * @param postId the post ID
     * @param postTitle the post title
     * @param reviewerName the name of the reviewer
     * @param reviewStatus the review status (approved/rejected)
     */
    @Transactional
    public void handlePostReviewed(Long postCreatorUserId, Long hubId, Long postId, String postTitle,
                                 String reviewerName, String reviewStatus) {
        logger.debug("Processing post review: creator={}, hub={}, post={}, status={}", postCreatorUserId, hubId, postId, reviewStatus);

        String title = "Post Reviewed";
        String message = String.format("%s %s your post '%s'", reviewerName, reviewStatus.toLowerCase(), postTitle);

        NotificationStorageService.EntityReferences entityRefs = NotificationStorageService.EntityReferences.post(hubId, postId);
        Map<String, Object> metadata = Map.of(
                "reviewer_name", reviewerName,
                "post_title", postTitle,
                "review_status", reviewStatus
        );

        notificationDispatcherService.dispatchNotification(
                NotificationType.POST_REVIEWED, title, message, List.of(postCreatorUserId), entityRefs, metadata);

        logger.info("Processed post review notification: creator={}, hub={}, post={}, status={}",
                   postCreatorUserId, hubId, postId, reviewStatus);
    }

    /**
     * Handles comment added to post event.
     *
     * @param hubId the collaboration hub ID
     * @param postId the post ID
     * @param commentId the comment ID
     * @param postTitle the post title
     * @param commenterName the name of the commenter
     * @param commentContent the comment content (truncated for notification)
     * @param notifyUserIds the user IDs to notify (post creator, other commenters, etc.)
     */
    @Transactional
    public void handleCommentAdded(Long hubId, Long postId, Long commentId, String postTitle, String commenterName,
                                 String commentContent, List<Long> notifyUserIds) {
        logger.debug("Processing comment added: hub={}, post={}, comment={}, commenter={}, recipients={}",
                    hubId, postId, commentId, commenterName, notifyUserIds.size());

        String title = "New Comment";
        String truncatedContent = commentContent.length() > 100
                ? commentContent.substring(0, 100) + "..."
                : commentContent;
        String message = String.format("%s commented on '%s': %s", commenterName, postTitle, truncatedContent);

        NotificationStorageService.EntityReferences entityRefs = NotificationStorageService.EntityReferences.comment(hubId, postId, commentId);
        Map<String, Object> metadata = Map.of(
                "commenter_name", commenterName,
                "post_title", postTitle,
                "comment_preview", truncatedContent
        );

        notificationDispatcherService.dispatchNotification(
                NotificationType.COMMENT_ADDED, title, message, notifyUserIds, entityRefs, metadata);

        logger.info("Processed comment added notification: hub={}, post={}, comment={}, recipients={}", hubId, postId, commentId, notifyUserIds.size());
    }

    /**
     * Handles user mentioned in comment event.
     *
     * @param mentionedUserIds the user IDs that were mentioned
     * @param hubId the collaboration hub ID
     * @param postId the post ID
     * @param commentId the comment ID
     * @param postTitle the post title
     * @param commenterName the name of the commenter
     * @param commentContent the comment content (truncated for notification)
     */
    @Transactional
    public void handleCommentMention(List<Long> mentionedUserIds, Long hubId, Long postId, Long commentId, String postTitle,
                                   String commenterName, String commentContent) {
        logger.debug("Processing comment mentions: hub={}, post={}, comment={}, commenter={}, mentioned={}",
                    hubId, postId, commentId, commenterName, mentionedUserIds.size());

        String title = "Mentioned in Comment";
        String truncatedContent = commentContent.length() > 100
                ? commentContent.substring(0, 100) + "..."
                : commentContent;
        String message = String.format("%s mentioned you in a comment on '%s': %s",
                                      commenterName, postTitle, truncatedContent);

        NotificationStorageService.EntityReferences entityRefs = NotificationStorageService.EntityReferences.comment(hubId, postId, commentId);
        Map<String, Object> metadata = Map.of(
                "commenter_name", commenterName,
                "post_title", postTitle,
                "comment_preview", truncatedContent
        );

        notificationDispatcherService.dispatchNotification(
                NotificationType.COMMENT_MENTION, title, message, mentionedUserIds, entityRefs, metadata);

        logger.info("Processed comment mention notifications: hub={}, post={}, comment={}, mentioned={}", hubId, postId, commentId, mentionedUserIds.size());
    }

    /**
     * Handles user mentioned in chat event.
     *
     * @param mentionedUserIds the user IDs that were mentioned
     * @param hubId the collaboration hub ID
     * @param chatChannelId the chat channel ID
     * @param chatChannelName the chat channel name
     * @param senderName the name of the message sender
     * @param messageContent the message content (truncated for notification)
     */
    @Transactional
    public void handleChatMention(List<Long> mentionedUserIds, Long hubId, Long chatChannelId, String chatChannelName,
                                String senderName, String messageContent) {
        logger.debug("Processing chat mentions: hub={}, channel={}, sender={}, mentioned={}",
                    hubId, chatChannelId, senderName, mentionedUserIds.size());

        String title = "Mentioned in Chat";
        String truncatedContent = messageContent.length() > 100
                ? messageContent.substring(0, 100) + "..."
                : messageContent;
        String message = String.format("%s mentioned you in '%s': %s", senderName, chatChannelName, truncatedContent);

        NotificationStorageService.EntityReferences entityRefs = NotificationStorageService.EntityReferences.chat(hubId, chatChannelId);
        Map<String, Object> metadata = Map.of(
                "sender_name", senderName,
                "channel_name", chatChannelName,
                "message_preview", truncatedContent
        );

        notificationDispatcherService.dispatchNotification(
                NotificationType.CHAT_MENTION, title, message, mentionedUserIds, entityRefs, metadata);

        logger.info("Processed chat mention notifications: hub={}, channel={}, mentioned={}", hubId, chatChannelId, mentionedUserIds.size());
    }

    /**
     * Handles user added to chat event.
     *
     * @param addedUserIds the user IDs that were added to the chat
     * @param hubId the collaboration hub ID
     * @param chatChannelId the chat channel ID
     * @param chatChannelName the chat channel name
     * @param adderName the name of the person who added them
     */
    @Transactional
    public void handleUserAddedToChat(List<Long> addedUserIds, Long hubId, Long chatChannelId, String chatChannelName, String adderName) {
        logger.debug("Processing chat additions: hub={}, channel={}, adder={}, added={}",
                    hubId, chatChannelId, adderName, addedUserIds.size());

        String title = "Added to Chat";
        String message = String.format("%s added you to the chat '%s'", adderName, chatChannelName);

        NotificationStorageService.EntityReferences entityRefs = NotificationStorageService.EntityReferences.chat(hubId, chatChannelId);
        Map<String, Object> metadata = Map.of(
                "adder_name", adderName,
                "channel_name", chatChannelName
        );

        notificationDispatcherService.dispatchNotification(
                NotificationType.CHAT_ADDED, title, message, addedUserIds, entityRefs, metadata);

        logger.info("Processed chat addition notifications: hub={}, channel={}, added={}", hubId, chatChannelId, addedUserIds.size());
    }

    // ========================================
    // ENHANCED METHODS FOR MIXED USER TYPES
    // ========================================

    /**
     * Enhanced method for comment mentions with proper parameter support.
     */
    @Transactional
    public void handleCommentMentionMixed(List<NotificationRecipient> recipients, Long hubId, Long postId, Long commentId, String postTitle,
                                         String commenterName, String commentContent) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("commenterName", commenterName);
        parameters.put("postTitle", postTitle != null ? postTitle : "Post #" + postId);
        parameters.put("commentPreview", truncateContent(commentContent, 100));

        NotificationStorageService.EntityReferences entityRefs =
                NotificationStorageService.EntityReferences.comment(hubId, postId, commentId);

        handleMixedNotificationWithParameters(NotificationType.COMMENT_MENTION, recipients, entityRefs, parameters);
    }

    /**
     * Enhanced method for chat mentions with proper parameter support.
     */
    @Transactional
    public void handleChatMentionMixed(List<NotificationRecipient> recipients, Long hubId, Long channelId, String channelName,
                                      String senderName, String messageContent) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("senderName", senderName);
        parameters.put("channelName", channelName);
        parameters.put("messagePreview", truncateContent(messageContent, 100));

        NotificationStorageService.EntityReferences entityRefs =
                NotificationStorageService.EntityReferences.chat(hubId, channelId);

        handleMixedNotificationWithParameters(NotificationType.CHAT_MENTION, recipients, entityRefs, parameters);
    }

    /**
     * Enhanced method for chat additions with proper parameter support.
     */
    @Transactional
    public void handleChatAddedMixed(List<NotificationRecipient> recipients, Long hubId, Long channelId, String channelName,
                                    String adderName) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("adderName", adderName);
        parameters.put("channelName", channelName);

        NotificationStorageService.EntityReferences entityRefs =
                NotificationStorageService.EntityReferences.chat(hubId, channelId);

        handleMixedNotificationWithParameters(NotificationType.CHAT_ADDED, recipients, entityRefs, parameters);
    }

    // ========================================
    // BRIEF NOTIFICATION METHODS
    // ========================================

    /**
     * Handles brief created event.
     *
     * @param notifyUserIds the user IDs to notify
     * @param hubId the collaboration hub ID
     * @param briefId the brief ID
     * @param briefTitle the brief title
     * @param creatorName the name of the brief creator
     */
    @Transactional
    public void handleBriefCreated(List<Long> notifyUserIds, Long hubId, Long briefId, String briefTitle, String creatorName) {
        logger.debug("Processing brief created: hub={}, brief={}, creator={}, recipients={}",
                    hubId, briefId, creatorName, notifyUserIds.size());

        String title = "New Brief Created";
        String message = String.format("%s created a new brief '%s'", creatorName, briefTitle);

        NotificationStorageService.EntityReferences entityRefs = NotificationStorageService.EntityReferences.brief(hubId, briefId);
        Map<String, Object> metadata = Map.of(
                "creator_name", creatorName,
                "brief_title", briefTitle
        );

        notificationDispatcherService.dispatchNotification(
                NotificationType.BRIEF_CREATED, title, message, notifyUserIds, entityRefs, metadata);

        logger.info("Processed brief created notification: hub={}, brief={}, recipients={}", hubId, briefId, notifyUserIds.size());
    }

    /**
     * Handles brief updated event.
     *
     * @param notifyUserIds the user IDs to notify
     * @param hubId the collaboration hub ID
     * @param briefId the brief ID
     * @param briefTitle the brief title
     * @param updaterName the name of the person who updated the brief
     */
    @Transactional
    public void handleBriefUpdated(List<Long> notifyUserIds, Long hubId, Long briefId, String briefTitle, String updaterName) {
        logger.debug("Processing brief updated: hub={}, brief={}, updater={}, recipients={}",
                    hubId, briefId, updaterName, notifyUserIds.size());

        String title = "Brief Updated";
        String message = String.format("%s updated the brief '%s'", updaterName, briefTitle);

        NotificationStorageService.EntityReferences entityRefs = NotificationStorageService.EntityReferences.brief(hubId, briefId);
        Map<String, Object> metadata = Map.of(
                "updater_name", updaterName,
                "brief_title", briefTitle
        );

        notificationDispatcherService.dispatchNotification(
                NotificationType.BRIEF_UPDATED, title, message, notifyUserIds, entityRefs, metadata);

        logger.info("Processed brief updated notification: hub={}, brief={}, recipients={}", hubId, briefId, notifyUserIds.size());
    }

    /**
     * Handles brief assigned event.
     *
     * @param assignedUserIds the user IDs that were assigned to the brief
     * @param hubId the collaboration hub ID
     * @param briefId the brief ID
     * @param briefTitle the brief title
     * @param assignerName the name of the person who made the assignment
     */
    @Transactional
    public void handleBriefAssigned(List<Long> assignedUserIds, Long hubId, Long briefId, String briefTitle, String assignerName) {
        logger.debug("Processing brief assigned: hub={}, brief={}, assigner={}, assigned={}",
                    hubId, briefId, assignerName, assignedUserIds.size());

        String title = "Assigned to Brief";
        String message = String.format("%s assigned you to work on the brief '%s'", assignerName, briefTitle);

        NotificationStorageService.EntityReferences entityRefs = NotificationStorageService.EntityReferences.brief(hubId, briefId);
        Map<String, Object> metadata = Map.of(
                "assigner_name", assignerName,
                "brief_title", briefTitle
        );

        notificationDispatcherService.dispatchNotification(
                NotificationType.BRIEF_ASSIGNED, title, message, assignedUserIds, entityRefs, metadata);

        logger.info("Processed brief assigned notification: hub={}, brief={}, assigned={}", hubId, briefId, assignedUserIds.size());
    }

    // ========================================
    // CORE MIXED NOTIFICATION HANDLING
    // ========================================

    /**
     * Handles notifications for mixed recipients with parameter-based internationalization.
     * This is the recommended approach for scalable notification handling.
     */
    @Transactional
    public void handleMixedNotificationWithParameters(NotificationType type, List<NotificationRecipient> recipients,
                                                     NotificationStorageService.EntityReferences entityReferences,
                                                     Map<String, Object> parameters) {

        logger.debug("Processing mixed notification with parameters: type={}, recipients={}", type, recipients.size());

        // Separate internal and external recipients
        List<Long> internalUserIds = new ArrayList<>();
        List<NotificationRecipient> externalRecipients = new ArrayList<>();

        for (NotificationRecipient recipient : recipients) {
            if (recipient.getUserId() != null) {
                internalUserIds.add(recipient.getUserId());
            } else {
                externalRecipients.add(recipient);
            }
        }

        // Handle internal users via standard notification system
        if (!internalUserIds.isEmpty()) {
            // Generate localized title and message for internal users (default locale for now)
            Locale locale = Locale.ENGLISH; // TODO: Get user's preferred locale
            String title = translationService.getNotificationTitle(type, locale);
            String message = translationService.getNotificationMessage(type, locale, parameters);

            notificationDispatcherService.dispatchNotification(
                    type, title, message, internalUserIds, entityReferences, parameters);

            logger.info("Sent notifications to {} internal users for type: {}",
                       internalUserIds.size(), type);
        }

        // Handle external users via localized email
        if (!externalRecipients.isEmpty()) {
            sendLocalizedEmailsToExternalRecipients(type, externalRecipients, parameters);

            logger.info("Sent localized emails to {} external recipients for type: {}",
                       externalRecipients.size(), type);
        }
    }

    /**
     * Legacy method for backward compatibility.
     * @deprecated Use handleMixedNotificationWithParameters instead
     */
    @Deprecated
    @Transactional
    public void handleMixedNotification(NotificationType type, String title, String message,
                                      List<NotificationRecipient> recipients,
                                      NotificationStorageService.EntityReferences entityReferences,
                                      Map<String, Object> metadata) {

        logger.debug("Processing mixed notification (legacy): type={}, recipients={}", type, recipients.size());

        // Separate internal and external recipients
        List<Long> internalUserIds = new ArrayList<>();
        List<NotificationRecipient> externalRecipients = new ArrayList<>();

        for (NotificationRecipient recipient : recipients) {
            if (recipient.getUserId() != null) {
                internalUserIds.add(recipient.getUserId());
            } else {
                externalRecipients.add(recipient);
            }
        }

        // Handle internal users via standard notification system
        if (!internalUserIds.isEmpty()) {
            notificationDispatcherService.dispatchNotification(
                    type, title, message, internalUserIds, entityReferences, metadata);

            logger.info("Sent notifications to {} internal users for type: {}",
                       internalUserIds.size(), type);
        }

        // Handle external users via direct email
        if (!externalRecipients.isEmpty()) {
            sendDirectEmailsToExternalRecipients(type, title, message, externalRecipients);

            logger.info("Sent direct emails to {} external recipients for type: {}",
                       externalRecipients.size(), type);
        }
    }

    /**
     * Sends localized emails to external recipients with parameter support.
     */
    private void sendLocalizedEmailsToExternalRecipients(NotificationType type, List<NotificationRecipient> externalRecipients,
                                                        Map<String, Object> parameters) {
        for (NotificationRecipient recipient : externalRecipients) {
            try {
                if (recipient.getEmail() != null && !recipient.getEmail().trim().isEmpty()) {
                    sendLocalizedEmailToExternalRecipient(type, recipient, parameters);
                    logger.debug("Sent localized email to external recipient: {}", recipient.getEmail());
                }
            } catch (Exception e) {
                logger.warn("Failed to send email to external recipient {}: {}", recipient.getEmail(), e.getMessage());
            }
        }
    }

    /**
     * Sends direct email notifications to external recipients with internationalization support.
     * @deprecated Use sendLocalizedEmailsToExternalRecipients instead
     */
    @Deprecated
    private void sendDirectEmailsToExternalRecipients(NotificationType type, String title, String message,
                                                     List<NotificationRecipient> externalRecipients) {
        for (NotificationRecipient recipient : externalRecipients) {
            try {
                if (recipient.getEmail() != null && !recipient.getEmail().trim().isEmpty()) {
                    emailService.sendSimpleEmail(recipient.getEmail(), title, message);
                    logger.debug("Sent direct email to external recipient: {}", recipient.getEmail());
                }
            } catch (Exception e) {
                logger.warn("Failed to send email to external recipient {}: {}", recipient.getEmail(), e.getMessage());
            }
        }
    }

    /**
     * Sends localized email to an external recipient.
     */
    private void sendLocalizedEmailToExternalRecipient(NotificationType type, NotificationRecipient recipient,
                                                      Map<String, Object> parameters) {
        try {
            // Default to English for external users (could be enhanced to detect user locale)
            Locale locale = Locale.ENGLISH;

            // Prepare parameters with recipient info
            Map<String, Object> emailParams = new HashMap<>();
            if (parameters != null) {
                emailParams.putAll(parameters);
            }

            // Add recipient name to parameters
            String recipientName = recipient.getName() != null && !recipient.getName().trim().isEmpty()
                    ? recipient.getName()
                    : "there";
            emailParams.put("recipientName", recipientName);

            // Get localized email content
            String subject = translationService.getEmailSubject(type, locale, emailParams);
            String body = translationService.getEmailBody(type, locale, emailParams);

            // Send email
            boolean sent = emailService.sendSimpleEmail(recipient.getEmail(), subject, body);
            if (sent) {
                logger.info("Sent localized {} email to external recipient: {}", type, recipient.getEmail());
            } else {
                logger.warn("Failed to send {} email to external recipient: {}", type, recipient.getEmail());
            }

        } catch (Exception e) {
            logger.error("Error sending localized email to {}: {}", recipient.getEmail(), e.getMessage());
        }
    }

    /**
     * Truncates content for preview purposes.
     */
    private String truncateContent(String content, int maxLength) {
        if (content == null || content.length() <= maxLength) {
            return content;
        }
        return content.substring(0, maxLength) + "...";
    }

    // ========================================
    // UTILITY METHODS FOR RECIPIENT CONVERSION
    // ========================================

    /**
     * Creates a NotificationRecipient from a HubParticipant.
     * Handles both internal and external participants automatically.
     */
    public static NotificationRecipient fromHubParticipant(org.jooq.generated.tables.pojos.HubParticipant participant) {
        if (participant.getUserId() != null) {
            // Internal user
            return new NotificationRecipient(participant.getUserId(), participant.getEmail(), participant.getName());
        } else {
            // External participant
            return new NotificationRecipient(participant.getEmail(), participant.getName());
        }
    }

    /**
     * Creates NotificationRecipients from a list of HubParticipants.
     */
    public static List<NotificationRecipient> fromHubParticipants(List<org.jooq.generated.tables.pojos.HubParticipant> participants) {
        return participants.stream()
                .map(NotificationEventService::fromHubParticipant)
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * Creates a NotificationRecipient for an internal user.
     */
    public static NotificationRecipient forInternalUser(Long userId, String email, String name) {
        return new NotificationRecipient(userId, email, name);
    }

    /**
     * Creates a NotificationRecipient for an external user.
     */
    public static NotificationRecipient forExternalUser(String email, String name) {
        return new NotificationRecipient(email, name);
    }

    /**
     * Data class representing a notification recipient (internal or external).
     */
    public static class NotificationRecipient {
        private final Long userId;        // For internal users
        private final String email;       // For external users
        private final String name;        // Display name

        // Constructor for internal users
        public NotificationRecipient(Long userId, String email, String name) {
            this.userId = userId;
            this.email = email;
            this.name = name;
        }

        // Constructor for external users (no userId)
        public NotificationRecipient(String email, String name) {
            this.userId = null;
            this.email = email;
            this.name = name;
        }

        public Long getUserId() { return userId; }
        public String getEmail() { return email; }
        public String getName() { return name; }

        public boolean isInternal() { return userId != null; }
        public boolean isExternal() { return userId == null; }
    }
}
