package com.collabhub.be.modules.notifications.service;

import com.collabhub.be.modules.auth.dto.UserType;
import com.collabhub.be.modules.auth.repository.UserRepository;
import com.collabhub.be.modules.notifications.constants.NotificationConstants;
import com.collabhub.be.modules.notifications.dto.NotificationType;
import org.jooq.generated.tables.pojos.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Main notification dispatcher service that coordinates notification delivery.
 * Handles routing between in-app and email channels based on user type and preferences.
 */
@Service
public class NotificationDispatcherService {

    private static final Logger logger = LoggerFactory.getLogger(NotificationDispatcherService.class);

    private final NotificationService notificationService;
    private final NotificationStorageService notificationStorageService;
    private final EmailNotificationService emailNotificationService;
    private final UserRepository userRepository;

    public NotificationDispatcherService(NotificationService notificationService,
                                       NotificationStorageService notificationStorageService,
                                       EmailNotificationService emailNotificationService,
                                       UserRepository userRepository) {
        this.notificationService = notificationService;
        this.notificationStorageService = notificationStorageService;
        this.emailNotificationService = emailNotificationService;
        this.userRepository = userRepository;
    }

    /**
     * Dispatches a notification to multiple users through appropriate channels.
     * Automatically handles internal vs external user routing.
     *
     * @param type the notification type
     * @param title the notification title
     * @param message the notification message
     * @param recipientUserIds the user IDs to notify
     * @param entityReferences optional entity references
     * @param metadata optional additional metadata
     */
    @Transactional
    public void dispatchNotification(NotificationType type, String title, String message,
                                   List<Long> recipientUserIds,
                                   NotificationStorageService.EntityReferences entityReferences,
                                   Map<String, Object> metadata) {
        
        logger.debug("Dispatching notification: type={}, recipients={}", type, recipientUserIds.size());

        if (recipientUserIds.isEmpty()) {
            logger.debug("No recipients for notification type: {}", type);
            return;
        }

        // Get delivery plan based on user preferences
        NotificationService.NotificationDeliveryPlan deliveryPlan = 
                notificationService.processNotificationEvent(type, recipientUserIds);

        // Handle in-app notifications for internal users
        if (deliveryPlan.hasInAppRecipients()) {
            handleInAppNotifications(type, title, message, deliveryPlan.getInAppRecipients(), 
                                   entityReferences, metadata);
        }

        // Handle email notifications for all users
        if (deliveryPlan.hasEmailRecipients()) {
            handleEmailNotifications(type, title, message, deliveryPlan.getEmailRecipients(), 
                                   entityReferences);
        }

        logger.info("Notification dispatch completed: type={}, inApp={}, email={}", 
                   type, deliveryPlan.getInAppRecipients().size(), deliveryPlan.getEmailRecipients().size());
    }

    /**
     * Convenience method for simple notifications without entity references.
     */
    @Transactional
    public void dispatchSimpleNotification(NotificationType type, String title, String message,
                                         List<Long> recipientUserIds) {
        dispatchNotification(type, title, message, recipientUserIds, null, null);
    }

    /**
     * Handles in-app notification delivery.
     */
    private void handleInAppNotifications(NotificationType type, String title, String message,
                                        Set<Long> recipientUserIds,
                                        NotificationStorageService.EntityReferences entityReferences,
                                        Map<String, Object> metadata) {
        
        logger.debug("Creating in-app notifications for {} users", recipientUserIds.size());

        notificationStorageService.createNotifications(type, title, message, recipientUserIds, 
                                                      entityReferences, metadata);

        logger.info("Created {} in-app notifications for type: {}", recipientUserIds.size(), type);
    }

    /**
     * Handles email notification delivery with user type filtering.
     */
    private void handleEmailNotifications(NotificationType type, String title, String message,
                                        Set<Long> recipientUserIds,
                                        NotificationStorageService.EntityReferences entityReferences) {
        
        logger.debug("Processing email notifications for {} users", recipientUserIds.size());

        // Filter recipients to ensure we have valid email addresses
        Set<Long> validEmailRecipients = filterValidEmailRecipients(recipientUserIds);

        if (validEmailRecipients.isEmpty()) {
            logger.warn("No valid email recipients found for notification type: {}", type);
            return;
        }

        // Send emails asynchronously with flood control
        emailNotificationService.sendEmailNotifications(type, title, message, 
                                                       validEmailRecipients, entityReferences);

        logger.info("Initiated email delivery for {} recipients, type: {}", 
                   validEmailRecipients.size(), type);
    }

    /**
     * Filters recipient user IDs to ensure they have valid email addresses.
     */
    private Set<Long> filterValidEmailRecipients(Set<Long> recipientUserIds) {
        List<User> users = userRepository.findByIds(recipientUserIds.stream().toList());
        
        return users.stream()
                .filter(user -> user.getEmail() != null && !user.getEmail().trim().isEmpty())
                .map(User::getId)
                .collect(Collectors.toSet());
    }
}
